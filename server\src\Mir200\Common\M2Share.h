#pragma once

// Mir200 M2Share - Core shared definitions and constants
// Based on delphi/EM2Engine/M2Share.pas - Following original project structure

#include "Types.h"
#include "Protocol.h"
#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <cmath>
#include <cstdarg>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <iomanip>

// Forward declarations
class BaseObject;
class PlayObject;
class UserEngine;
class LocalDatabase;

// Temporarily create minimal stub classes for compilation
class Environment {
public:
    std::string GetMapName() const { return ""; }
    bool Initialize() { return true; }
    void ProcessEnvironment() {}
    bool IsActive() const { return true; }
    void Finalize() {}
    void SaveEnvironmentData() {}
};

class Guild {
public:
    bool Initialize() { return true; }
    void ProcessGuild() {}
    void SaveGuildData() {}
    void Finalize() {}
};

class Castle {
public:
    void ProcessCastle() {}
    void SaveCastleData() {}
    void Finalize() {}
};

// Version information (from original M2Share.pas)
constexpr const char* G_VERSION = "1.00 Build 20140909";
constexpr const char* G_PROGRAM_NAME = "HAPPYM2.NET";
constexpr const char* G_WEBSITE = "http://WWW.HAPPYM2.NET";

// Constants from original M2Share.pas
constexpr int MAXLEVEL = 65535;
constexpr int MAXCHANGELEVEL = 1000;
constexpr int MAXMAGIC = 20;
constexpr int MAXUPLEVEL = 65535;
constexpr int MAXHUMPOWER = 65535;
constexpr double BODYLUCKUNIT_DOUBLE = 5.0E3;

// Skill constants (exact values from M2Share.pas)
constexpr BYTE SKILL_ONESWORD = 3;
constexpr BYTE SKILL_ILKWANG = 4;
constexpr BYTE SKILL_YEDO = 7;
constexpr BYTE SKILL_ERGUM = 12;
constexpr BYTE SKILL_BANWOL = 25;
constexpr BYTE SKILL_FIRESWORD = 26;
constexpr BYTE SKILL_MOOTEBO = 27;
constexpr BYTE SKILL_FIREBALL = 1;
constexpr BYTE SKILL_FIREBALL2 = 5;
constexpr BYTE SKILL_HEALLING = 2;
constexpr BYTE SKILL_AMYOUNSUL = 6;
constexpr BYTE SKILL_FIREWIND = 8;
constexpr BYTE SKILL_FIRE = 9;
constexpr BYTE SKILL_SHOOTLIGHTEN = 10;
constexpr BYTE SKILL_LIGHTENING = 11;
constexpr BYTE SKILL_FIRECHARM = 13;
constexpr BYTE SKILL_HANGMAJINBUB = 14;
constexpr BYTE SKILL_DEJIWONHO = 15;
constexpr BYTE SKILL_HOLYSHIELD = 16;
constexpr BYTE SKILL_SKELLETON = 17;
constexpr BYTE SKILL_CLOAK = 18;
constexpr BYTE SKILL_BIGCLOAK = 19;
constexpr BYTE SKILL_TAMMING = 20;
constexpr BYTE SKILL_SPACEMOVE = 21;
constexpr BYTE SKILL_EARTHFIRE = 22;
constexpr BYTE SKILL_FIREBOOM = 23;
constexpr BYTE SKILL_LIGHTFLOWER = 24;
constexpr BYTE SKILL_SHOWHP = 28;
constexpr BYTE SKILL_BIGHEALLING = 29;
constexpr BYTE SKILL_SINSU = 30;
constexpr BYTE SKILL_SHIELD = 31;
constexpr BYTE SKILL_KILLUNDEAD = 32;
constexpr BYTE SKILL_SNOWWIND = 33;
constexpr BYTE SKILL_UNAMYOUNSUL = 34;
constexpr BYTE SKILL_WINDTEBO = 35;
constexpr BYTE SKILL_MABE = 36;
constexpr BYTE SKILL_GROUPLIGHTENING = 37;
constexpr BYTE SKILL_GROUPAMYOUNSUL = 38;
constexpr BYTE SKILL_GROUPDEDING = 39;

// Extended skills
constexpr BYTE SKILL_40 = 40;
constexpr BYTE SKILL_41 = 41;
constexpr BYTE SKILL_42 = 42;
constexpr BYTE SKILL_43 = 43;
constexpr BYTE SKILL_44 = 44;
constexpr BYTE SKILL_45 = 45;
constexpr BYTE SKILL_46 = 46;
constexpr BYTE SKILL_47 = 47;
constexpr BYTE SKILL_48 = 48;
constexpr BYTE SKILL_49 = 49;
constexpr BYTE SKILL_50 = 50;
constexpr BYTE SKILL_51 = 51;
constexpr BYTE SKILL_52 = 52;
constexpr BYTE SKILL_53 = 53;
constexpr BYTE SKILL_54 = 54;
constexpr BYTE SKILL_55 = 55;
constexpr BYTE SKILL_56 = 56;
constexpr BYTE SKILL_57 = 57;
constexpr BYTE SKILL_58 = 58;
constexpr BYTE SKILL_59 = 59;
constexpr BYTE SKILL_60 = 60;
constexpr BYTE SKILL_61 = 61;
constexpr BYTE SKILL_62 = 62;
constexpr BYTE SKILL_63 = 63;
constexpr BYTE SKILL_64 = 64;
constexpr BYTE SKILL_65 = 65;
constexpr BYTE SKILL_66 = 66;
constexpr BYTE SKILL_67 = 67;
constexpr BYTE SKILL_68 = 68;
constexpr BYTE SKILL_69 = 69;
constexpr BYTE SKILL_70 = 80;
constexpr BYTE SKILL_71 = 81;
constexpr BYTE SKILL_72 = 82;

// Monster constants
constexpr int SLAVEMAXLEVEL = 9;
constexpr int MONSTER_SANDMOB = 3;
constexpr int MONSTER_ROCKMAN = 4;
constexpr int MONSTER_RON = 9;
constexpr int MONSTER_MINORNUMA = 18;
constexpr int ANIMAL_CHICKEN = 20;
constexpr int ANIMAL_DEER = 21;
constexpr int MONSTER_OMA = 23;
constexpr int MONSTER_OMAKNIGHT = 25;
constexpr int MONSTER_OMAWARRIOR = 27;
constexpr int MONSTER_SPITSPIDER = 30;
constexpr int MONSTER_STICK = 39;
constexpr int MONSTER_DUALAXE = 42;
constexpr int MONSTER_THONEDARK = 74;
constexpr int MONSTER_LIGHTZOMBI = 78;
constexpr int MONSTER_WHITESKELETON = 94;
constexpr int MONSTER_BEEQUEEN = 124;
constexpr int MONSTER_BEE = 125;
constexpr int MONSTER_MAGUNGSA = 143;
constexpr int MONSTER_SCULTURE1 = 145;
constexpr int MONSTER_SCULTURE2 = 148;
constexpr int MONSTER_SCULTUREKING = 152;
constexpr int MONSTER_ELFMONSTER = 161;
constexpr int MONSTER_ELFWARRIOR = 162;

// NPC constants
constexpr int SUPREGUARD = 11;
constexpr int CHICKEN = 51;
constexpr int DEER = 52;
constexpr int WOLF = 53;
constexpr int TRAINER = 55;

// String constants
constexpr const char* S_MAN = "MAN";
constexpr const char* S_SUNRAISE = "SUNRAISE";
constexpr const char* S_DAY = "DAY";
constexpr const char* S_SUNSET = "SUNSET";
constexpr const char* S_NIGHT = "NIGHT";
constexpr const char* S_WARRIOR = "WARRIOR";
constexpr const char* S_WIZARD = "WIZARD";
constexpr const char* S_TAOS = "TAOIST";

// Day of week constants
constexpr const char* S_SUN = "SUN";
constexpr const char* S_MON = "MON";
constexpr const char* S_TUE = "TUE";
constexpr const char* S_WED = "WED";
constexpr const char* S_THU = "THU";
constexpr const char* S_FRI = "FRI";
constexpr const char* S_SAT = "SAT";

// Default values are now defined in Types.h

// Job type constants (matching original)
constexpr BYTE WARR = 0;
constexpr BYTE WIZARD = 1;
constexpr BYTE TAOS = 2;

// Size constants
constexpr int SIZEOFTHUMAN = 0xC5C;

// Additional type definitions for UserEngine compatibility
// Note: Temporarily simplified for compilation - will be expanded later

// Temporarily removed complex structures for compilation
// These will be re-added once basic compilation works

// Additional message and color types
enum TMsgType {
    t_Notice = 0,
    t_Hint = 1,
    t_System = 2,
    t_Say = 3,
    t_GM = 4
};

enum TMsgColor {
    c_Red = 0,
    c_Green = 1,
    c_Blue = 2,
    c_Yellow = 3,
    c_White = 4
};

// Global variables (following original M2Share.pas structure)
namespace g_config {
    extern bool server_ready;
    extern bool show_exception;
    extern bool show_pre_fix;
    extern std::string server_name;
    extern std::string gate_addr;
    extern int gate_port;
    extern int max_user;
    extern bool test_server;
    extern bool service_mode;
    extern std::string version_date;
    extern DWORD server_file_crc;

    // Message processing configuration (following original project)
    extern bool bo_spell_send_update_msg;
    extern bool bo_action_send_action_msg;
}

// Global objects (following original structure)
namespace g_objects {
    extern std::shared_ptr<UserEngine> user_engine;
    extern std::shared_ptr<LocalDatabase> local_database;
    extern std::vector<std::shared_ptr<Environment>> env_list;
    extern std::unordered_map<std::string, std::shared_ptr<Guild>> guild_list;
    extern std::shared_ptr<Castle> castle;
}

// Global functions
namespace g_functions {
    // Time functions
    DWORD GetCurrentTime();
    std::string GetCurrentTimeStr();
    std::string GetDateTimeStr();
    
    // Random functions
    int Random(int max);
    int Random(int min, int max);
    bool RandomBool();
    
    // String functions
    std::string GetValidStr3(const std::string& str, std::string& dest, const std::string& div);
    std::string GetValidStrCap(const std::string& str, std::string& dest, const std::string& div);
    std::string ArrestStringEx(const std::string& str, const std::string& search_str, 
                              const std::string& end_str, std::string& result);
    
    // File functions
    bool FileExists(const std::string& filename);
    bool DirectoryExists(const std::string& dirname);
    std::string ExtractFilePath(const std::string& filename);
    std::string ExtractFileName(const std::string& filename);
    std::string ChangeFileExt(const std::string& filename, const std::string& ext);
    
    // Calculation functions
    DWORD CalcFileCRC(const std::string& filename);
    int GetDistance(int x1, int y1, int x2, int y2);
    BYTE GetDirection(int x1, int y1, int x2, int y2);
    Point GetFrontPosition(int x, int y, BYTE dir);
    Point GetBackPosition(int x, int y, BYTE dir);
    
    // Level and experience functions
    DWORD GetLevelExp(BYTE level);
    BYTE GetLevelByExp(DWORD exp);
    DWORD GetUpgradeExp(BYTE level);
    
    // Item functions
    bool IsAccessory(int std_mode);
    bool IsWeapon(int std_mode);
    bool IsArmor(int std_mode);
    std::string GetItemGradeStr(const UserItem& item);
    
    // Magic functions
    bool IsWarriorSkill(WORD magic_id);
    bool IsWizardSkill(WORD magic_id);
    bool IsTaoistSkill(WORD magic_id);
    std::string GetMagicName(WORD magic_id);
    
    // Color and display functions
    std::string GetGoldStr(int gold);
    std::string GetUserName(const std::string& account, const std::string& char_name);
    
    // Network functions
    std::string EncodeMessage(const DefaultMessage& msg);
    DefaultMessage DecodeMessage(const std::string& data);
    std::string EncodeString(const std::string& str);
    std::string DecodeString(const std::string& data);
    
    // Log functions
    void MainOutMessage(const std::string& msg);
    void MainOutMessageFmt(const char* format, ...);
}

// Initialization and cleanup functions
bool InitializeM2Share();
void FinalizeM2Share();

// Configuration functions
bool LoadServerConfig(const std::string& config_file);
bool SaveServerConfig(const std::string& config_file);

// Utility macros
#define SAFE_DELETE(p) { if(p) { delete (p); (p) = nullptr; } }
#define SAFE_DELETE_ARRAY(p) { if(p) { delete[] (p); (p) = nullptr; } }

// Debug macros
#ifdef _DEBUG
#define DEBUG_MSG(msg) g_functions::MainOutMessage("[DEBUG] " + std::string(msg))
#define DEBUG_MSGFMT(fmt, ...) g_functions::MainOutMessageFmt("[DEBUG] " fmt, __VA_ARGS__)
#else
#define DEBUG_MSG(msg)
#define DEBUG_MSGFMT(fmt, ...)
#endif

// Error handling macros
#define TRY_BEGIN try {
#define TRY_END } catch (const std::exception& e) { \
    g_functions::MainOutMessage("[ERROR] Exception: " + std::string(e.what())); \
} catch (...) { \
    g_functions::MainOutMessage("[ERROR] Unknown exception occurred"); \
}
