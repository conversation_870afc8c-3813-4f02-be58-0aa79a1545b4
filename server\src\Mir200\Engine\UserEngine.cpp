#include "UserEngine.h"
#include "Common/M2Share.h"
#include <chrono>
#include <algorithm>

// Constructor - Initialize all variables following original TUserEngine.Create
UserEngine::UserEngine() {
    // Initialize timing variables
    DWORD current_tick = GetTickCount();
    m_show_online_tick = current_tick;
    m_send_online_hum_time = current_tick;
    m_process_map_door_tick = current_tick;
    m_process_missions_time = current_tick;
    m_process_monsters_tick = current_tick;
    m_regen_monsters_tick = current_tick;
    m_process_load_play_tick = current_tick;
    m_save_data_tick = current_tick;
    m_time_34 = current_tick;
    m_search_tick = current_tick;
    m_get_today_date_tick = current_tick;

    // Initialize processing indices and counters
    m_curr_mon_gen = 0;
    m_mon_gen_list_position = 0;
    m_mon_gen_cert_list_position = 0;
    m_proc_hum_idx = 0;
    m_process_human_loop_time = 0;
    m_merchant_position = 0;
    m_npc_position = 0;

    // Initialize limits and configuration (following original values)
    m_limit_number = 1000000;      // 20080630 注释
    m_limit_user_count = 1000000;  // 20080630 注释

    // Initialize monster processing variables
    m_monster_count = 0;
    m_monster_process_position = 0;
    m_84 = 0;
    m_monster_process_count = 0;
    m_item_event = false;
    m_90 = 1800000;
    m_process_merchant_time_min = 0;
    m_process_merchant_time_max = 0;
    m_process_npc_time_min = 0;
    m_process_npc_time_max = 0;

    // Initialize flags
    m_start_load_magic = false;
    m_today_date = std::chrono::system_clock::now();

    // Initialize state
    m_initialized = false;
    m_running = false;

    // Initialize statistics
    m_online_user_count = 0;
    m_total_user_count = 0;
    m_max_user_count = 1000;

    // Initialize component references
    m_local_database = nullptr;
}

// Destructor - Clean up all resources following original TUserEngine.Destroy
UserEngine::~UserEngine() {
    TRY_BEGIN
        Finalize();

        // Clear all user open info
        for (auto& user_info : m_load_play_list) {
            // user_info will be automatically cleaned up by shared_ptr
        }
        m_load_play_list.clear();

        // Clear all play objects
        for (auto& pair : m_play_object_list) {
            // pair.second will be automatically cleaned up by shared_ptr
        }
        m_play_object_list.clear();

        // Clear free list
        m_play_object_free_list.clear();

        // Clear gold change info
        m_change_human_db_gold_list.clear();

        // Clear item list
        m_std_item_list.clear();

        // Clear monster list and associated data
        m_monster_list.clear();

        // Clear monster generation list
        for (auto& mon_gen : m_mon_gen_list) {
            if (mon_gen) {
                mon_gen->cert_list.clear();
            }
        }
        m_mon_gen_list.clear();

        // Clear other lists
        m_mon_free_list.clear();
        m_magic_list.clear();
        m_map_mon_gen_count_list.clear();
        m_admin_list.clear();
        m_merchant_list.clear();
        m_quest_npc_list.clear();
        m_list_70.clear();
        m_change_server_list.clear();
        m_magic_event_list.clear();
        m_play_object_level_list.clear();
        m_new_human_list.clear();
        m_list_of_gate_idx.clear();
        m_list_of_socket.clear();
        m_old_magic_list.clear();

    TRY_END
}

// Initialize - Following original TUserEngine.Initialize
bool UserEngine::Initialize() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing UserEngine...");

        // Initialize NPC and Merchant systems
        MerchantInitialize();
        NPCInitialize();

        // Initialize monster generation race information
        for (auto& mon_gen : m_mon_gen_list) {
            if (mon_gen) {
                mon_gen->race = GetMonRace(mon_gen->mon_name);
            }
        }

        m_initialized = true;
        g_functions::MainOutMessage("UserEngine initialized successfully");
        return true;

    TRY_END

    return false;
}

// Finalize - Clean up and save data
void UserEngine::Finalize() {
    TRY_BEGIN
        if (!m_initialized) return;

        g_functions::MainOutMessage("Finalizing UserEngine...");

        // Stop if running
        if (m_running) {
            Stop();
        }

        // Save all user data
        SaveAllUsers();

        // Clear all data structures
        {
            std::lock_guard<std::mutex> lock(m_load_play_section);
            m_load_play_list.clear();
        }

        m_play_object_list.clear();
        m_string_list_0c.clear();
        m_play_object_free_list.clear();
        m_change_human_db_gold_list.clear();

        m_initialized = false;
        g_functions::MainOutMessage("UserEngine finalized");

    TRY_END
}

// Start - Begin UserEngine processing
bool UserEngine::Start() {
    TRY_BEGIN
        if (!m_initialized) {
            g_functions::MainOutMessage("Error: UserEngine not initialized");
            return false;
        }

        if (m_running) {
            g_functions::MainOutMessage("UserEngine is already running");
            return true;
        }

        g_functions::MainOutMessage("Starting UserEngine...");

        m_running = true;
        g_functions::MainOutMessage("UserEngine started successfully");
        return true;

    TRY_END

    return false;
}

// Stop - Stop UserEngine processing
void UserEngine::Stop() {
    TRY_BEGIN
        if (!m_running) {
            g_functions::MainOutMessage("UserEngine is not running");
            return;
        }

        g_functions::MainOutMessage("Stopping UserEngine...");

        // Save all user data before stopping
        SaveAllUsers();

        m_running = false;
        g_functions::MainOutMessage("UserEngine stopped successfully");

    TRY_END
}

// ClearItemList - Clear item list (virtual method)
void UserEngine::ClearItemList() {
    TRY_BEGIN
        m_std_item_list.clear();
        g_functions::MainOutMessage("Item list cleared");
    TRY_END
}

// SwitchMagicList - Switch magic list
void UserEngine::SwitchMagicList() {
    TRY_BEGIN
        // Implementation for switching magic lists
        // This would involve swapping current magic list with old magic list
        if (!m_old_magic_list.empty()) {
            m_magic_list.swap(m_old_magic_list);
            g_functions::MainOutMessage("Magic list switched");
        }
    TRY_END
}

// SaveAllUsers - Save all user data
void UserEngine::SaveAllUsers() {
    TRY_BEGIN
        g_functions::MainOutMessage("Saving all user data...");

        int saved_count = 0;
        for (auto& pair : m_play_object_list) {
            if (pair.second) {
                SaveHumanRcd(pair.second);
                saved_count++;
            }
        }

        g_functions::MainOutMessage("Saved " + std::to_string(saved_count) + " user records");

    TRY_END
}

// Main processing methods
void UserEngine::Run() {
    TRY_BEGIN
        if (!m_running) return;

        // Main processing loop - following original TUserEngine.Execute
        ProcessData();

    TRY_END
}

void UserEngine::ProcessData() {
    TRY_BEGIN
        if (!m_running) return;

        // Process all subsystems in order
        ProcessHumans();
        ProcessMonsters();
        ProcessMerchants();
        ProcessNpcs();
        ProcessMissions();
        ProcessEvents();
        ProcessMapDoor();

    TRY_END
}

void UserEngine::Execute() {
    TRY_BEGIN
        // Main execution method - calls ProcessData
        ProcessData();
    TRY_END
}

// ProcessHumans - Main human processing method (based on original TUserEngine.ProcessHumans)
void UserEngine::ProcessHumans() {
    TRY_BEGIN
        DWORD current_tick = GetTickCount();
        bool check_time_limit = false;

        // Process load play list every 200ms
        if ((current_tick - m_process_load_play_tick) > 200) {
            m_process_load_play_tick = current_tick;

            std::lock_guard<std::mutex> lock(m_load_play_section);

            // Process pending user logins
            for (auto it = m_load_play_list.begin(); it != m_load_play_list.end(); ) {
                auto user_open_info = *it;
                if (!user_open_info) {
                    it = m_load_play_list.erase(it);
                    continue;
                }

                // Check if user is already logged in
                bool is_logged = false;
                auto play_it = m_play_object_list.find(user_open_info->char_name);
                if (play_it != m_play_object_list.end()) {
                    is_logged = true;
                }

                if (!is_logged) {
                    // Create new human player
                    auto play_object = MakeNewHuman(user_open_info);
                    if (play_object) {
                        m_play_object_list[user_open_info->char_name] = play_object;
                        m_new_human_list.push_back(play_object);
                        m_online_user_count++;
                        m_total_user_count++;
                    }
                } else {
                    // Kick existing user
                    KickOnlineUser(user_open_info->char_name);
                }

                it = m_load_play_list.erase(it);
            }

            // Process gold change requests
            for (auto& gold_info : m_change_human_db_gold_list) {
                if (gold_info) {
                    auto play_object = GetPlayObject(gold_info->game_master_name);
                    if (play_object) {
                        // Process gold change - placeholder for actual implementation
                        // play_object->GoldChange(gold_info->get_gold_user, gold_info->gold);
                    }
                }
            }
            m_change_human_db_gold_list.clear();
        }

        // Process new human list
        for (auto& play_object : m_new_human_list) {
            if (play_object) {
                // Set gate user list - placeholder for actual implementation
                // RunSocket.SetGateUserList(play_object->GetGateIdx(), play_object->GetSocket(), play_object);
            }
        }
        m_new_human_list.clear();

        // Process gate and socket lists
        for (auto gate_idx : m_list_of_gate_idx) {
            for (auto socket : m_list_of_socket) {
                // Close user - placeholder for actual implementation
                // RunSocket.CloseUser(gate_idx, socket);
            }
        }
        m_list_of_gate_idx.clear();
        m_list_of_socket.clear();

        // Process free list - clean up expired players
        DWORD check_time = current_tick;
        for (auto it = m_play_object_free_list.begin(); it != m_play_object_free_list.end(); ) {
            auto play_object = *it;
            if (!play_object) {
                it = m_play_object_free_list.erase(it);
                continue;
            }

            // Check if player should be freed (5 minutes default)
            if ((current_tick - play_object->GetGhostTick()) > 300000) {  // 5 * 60 * 1000
                it = m_play_object_free_list.erase(it);
                continue;
            } else {
                // Handle switch data if needed
                if (play_object->IsSwitchData() && play_object->IsRcdSaved()) {
                    if (SendSwitchData(play_object, play_object->GetServerIndex()) ||
                        play_object->GetWriteChgDataErrCount() > 20) {
                        play_object->SetSwitchData(false);
                        play_object->SetSwitchDataSended(true);
                        play_object->SetChgDataWritedTick(current_tick);
                    } else {
                        play_object->IncWriteChgDataErrCount();
                    }
                }

                if (play_object->IsSwitchDataSended() &&
                    ((current_tick - play_object->GetChgDataWritedTick()) > 100)) {
                    play_object->SetSwitchDataSended(false);
                    SendChangeServer(play_object, play_object->GetServerIndex());
                }
                ++it;
            }
        }

        // License check and today date update (simplified from original)
        if ((current_tick - m_search_tick) > 3600000) {  // 1 hour
            auto now = std::chrono::system_clock::now();
            auto today = std::chrono::system_clock::to_time_t(now);
            auto stored_today = std::chrono::system_clock::to_time_t(m_today_date);

            if (today != stored_today) {
                m_today_date = now;
            }

            m_search_tick = current_tick;
            // Keep original license values for compatibility
            m_limit_number = 1000000;
            m_limit_user_count = 1000000;
        }

        // Process online players
        int idx = m_proc_hum_idx;
        auto it = m_play_object_list.begin();
        std::advance(it, std::min(idx, static_cast<int>(m_play_object_list.size())));

        while (it != m_play_object_list.end()) {
            auto play_object = it->second;
            if (play_object) {
                if ((current_tick - play_object->GetRunTick()) > play_object->GetRunTime()) {
                    play_object->SetRunTick(current_tick);

                    if (!play_object->IsGhost()) {
                        if (!play_object->IsLoginNoticeOK()) {
                            play_object->RunNotice();
                        } else {
                            if (!play_object->IsReadyRun()) {
                                play_object->SetNotOnlineAddExp(false);
                                play_object->SetReadyRun(true);
                                play_object->UserLogon();
                            } else {
                                // Process player search and game time
                                if ((current_tick - play_object->GetSearchTick()) > play_object->GetSearchTime()) {
                                    play_object->SetSearchTick(current_tick);
                                    play_object->SearchViewRange();
                                    play_object->GameTimeChanged();
                                }
                            }

                            // Process line notices
                            ProcessLineNotices(play_object, current_tick);

                            // Run player main loop
                            play_object->Run();

                            // Save player data periodically
                            if ((current_tick - play_object->GetSaveRcdTick()) > 600000) {  // 10 minutes
                                play_object->SetSaveRcdTick(current_tick);
                                play_object->DealCancelA();
                                SaveHumanRcd(play_object);
                            }
                        }
                    } else {
                        // Player is ghost - remove from list
                        it = m_play_object_list.erase(it);
                        play_object->Disappear();
                        AddToHumanFreeList(play_object);
                        play_object->DealCancelA();
                        SaveHumanRcd(play_object);
                        // Close user connection - placeholder
                        // RunSocket.CloseUser(play_object->GetGateIdx(), play_object->GetSocket());
                        m_online_user_count--;
                        continue;
                    }
                }
            }

            ++it;
            idx++;

            // Check time limit to prevent blocking
            if ((GetTickCount() - check_time) > 50) {  // 50ms limit
                check_time_limit = true;
                m_proc_hum_idx = idx;
                break;
            }
        }

        if (!check_time_limit) {
            m_proc_hum_idx = 0;
        }

        m_process_human_loop_time++;

    TRY_END
}

// Helper method for processing line notices
void UserEngine::ProcessLineNotices(std::shared_ptr<PlayObject> play_object, DWORD current_tick) {
    TRY_BEGIN
        // Process line notices - placeholder for actual implementation
        // This would show rolling notices to players
        if ((current_tick - play_object->GetShowLineNoticeTick()) > 30000) {  // 30 seconds
            play_object->SetShowLineNoticeTick(current_tick);

            // Show line notice - placeholder
            // if (LineNoticeList.Count > play_object->GetShowLineNoticeIdx()) {
            //     std::string notice_msg = LineNoticeList[play_object->GetShowLineNoticeIdx()];
            //     play_object->SysMsg(notice_msg, c_Blue, t_Notice);
            // }

            play_object->IncShowLineNoticeIdx();
        }
    TRY_END
}

// MakeNewHuman - Create new human player (based on original MakeNewHuman function)
std::shared_ptr<PlayObject> UserEngine::MakeNewHuman(std::shared_ptr<UserOpenInfo> user_open_info) {
    TRY_BEGIN
        if (!user_open_info) return nullptr;

        // Create new PlayObject - placeholder for actual implementation
        auto play_object = std::make_shared<PlayObject>();
        if (!play_object) return nullptr;

        // Initialize player data from user_open_info
        GetHumData(play_object, user_open_info->hum_data);

        // Set basic properties
        play_object->SetRaceServer(RC_PLAYOBJECT);
        play_object->SetUserID(user_open_info->load_user.account);
        play_object->SetCharName(user_open_info->char_name);
        play_object->SetSocket(user_open_info->load_user.socket);
        play_object->SetGateIdx(user_open_info->load_user.gate_idx);
        play_object->SetSessionID(user_open_info->load_user.session_id);

        // Set home map if empty
        if (play_object->GetHomeMap().empty()) {
            int home_x, home_y;
            std::string home_map = GetHomeInfo(home_x, home_y);
            play_object->SetHomeMap(home_map);
            play_object->SetHomeX(home_x);
            play_object->SetHomeY(home_y);
            play_object->SetMapName(home_map);
            play_object->SetCurrX(GetRandHomeX(play_object));
            play_object->SetCurrY(GetRandHomeY(play_object));

            // Set initial abilities for new character
            if (play_object->GetLevel() <= 0) {
                play_object->SetLevel(1);
                play_object->SetHP(15);
                play_object->SetMaxHP(15);
                play_object->SetMP(15);
                play_object->SetMaxMP(15);
                play_object->SetExp(10);
                play_object->SetMaxExp(100);
                play_object->SetNewHuman(true);
            }
        }

        // Find environment for player's map
        auto envir = g_MapManager.GetMapInfo(g_nServerIndex, play_object->GetMapName());
        if (!envir) {
            // If map not found, move to home map
            play_object->SetMapName(play_object->GetHomeMap());
            play_object->SetCurrX(play_object->GetHomeX());
            play_object->SetCurrY(play_object->GetHomeY());
            envir = g_MapManager.FindMap(play_object->GetHomeMap());
        }

        // Handle death state
        if (play_object->GetHP() <= 0) {
            play_object->ClearStatusTime();
            if (play_object->GetPKLevel() < 2) {
                // Normal player death - go to home
                play_object->SetMapName(play_object->GetHomeMap());
                play_object->SetCurrX(play_object->GetHomeX() - 2 + (rand() % 5));
                play_object->SetCurrY(play_object->GetHomeY() - 2 + (rand() % 5));
            } else {
                // Red player death - go to red death map
                play_object->SetMapName("3");  // Red death map
                play_object->SetCurrX(839 + (rand() % 13));
                play_object->SetCurrY(668 + (rand() % 13));
            }
            play_object->SetHP(14);
        }

        // Set environment
        play_object->SetPEnvir(envir);
        if (!envir) {
            g_functions::MainOutMessage("Error: PlayObject.PEnvir = nil");
            return nullptr;
        }

        // Validate position
        int try_count = 0;
        while (!envir->CanWalk(play_object->GetCurrX(), play_object->GetCurrY(), true) && try_count < 5) {
            play_object->SetCurrX(play_object->GetCurrX() - 3 + (rand() % 6));
            play_object->SetCurrY(play_object->GetCurrY() - 3 + (rand() % 6));
            try_count++;
        }

        if (!envir->CanWalk(play_object->GetCurrX(), play_object->GetCurrY(), true)) {
            // Still can't walk, move to safe position
            play_object->SetMapName(g_Config.sHomeMap);
            play_object->SetCurrX(g_Config.nHomeX);
            play_object->SetCurrY(g_Config.nHomeY);
            envir = g_MapManager.FindMap(g_Config.sHomeMap);
            play_object->SetPEnvir(envir);
        }

        // Copy abilities to working abilities
        play_object->AbilCopyToWAbil();

        // Set ready state
        play_object->SetReadyRun(false);

        return play_object;

    TRY_END

    return nullptr;
}

// ProcessMonsters - Process all monsters (based on original ProcessMonsters)
void UserEngine::ProcessMonsters() {
    TRY_BEGIN
        DWORD current_tick = GetTickCount();
        bool process_limit = false;

        // Process monsters with time slicing
        int i = m_monster_process_position;
        int processed_count = 0;

        // Process monster list - placeholder for actual monster processing
        // This would iterate through all monsters and update their AI

        // Update monster processing statistics
        m_monster_process_count = processed_count;

        if (!process_limit) {
            m_monster_process_position = 0;
        }

        // Update timing statistics
        DWORD process_time = GetTickCount() - current_tick;
        // Update min/max processing times

    TRY_END
}

// ProcessMerchants - Process all merchants (based on original ProcessMerchants)
void UserEngine::ProcessMerchants() {
    TRY_BEGIN
        DWORD run_tick = GetTickCount();
        bool process_limit = false;

        int i = m_merchant_position;
        for (auto it = m_merchant_list.begin(); it != m_merchant_list.end(); ) {
            if (i-- > 0) {
                ++it;
                continue;
            }

            auto merchant = *it;
            if (!merchant) {
                it = m_merchant_list.erase(it);
                continue;
            }

            if (!merchant->IsGhost()) {
                DWORD current_tick = GetTickCount();
                if ((current_tick - merchant->GetRunTick()) > merchant->GetRunTime()) {
                    if ((current_tick - merchant->GetSearchTick()) > merchant->GetSearchTime()) {
                        merchant->SetSearchTick(current_tick);
                        merchant->SearchObjectViewRange();
                    }

                    if ((current_tick - merchant->GetRunTick()) > merchant->GetRunTime()) {
                        merchant->SetRunTick(current_tick);
                        merchant->Run();
                    }
                }
            } else {
                // Remove ghost merchants after 60 seconds
                if ((GetTickCount() - merchant->GetGhostTick()) > 60000) {
                    it = m_merchant_list.erase(it);
                    continue;
                }
            }

            // Check time limit
            if ((GetTickCount() - run_tick) > 50) {  // 50ms limit
                m_merchant_position = std::distance(m_merchant_list.begin(), it);
                process_limit = true;
                break;
            }

            ++it;
        }

        if (!process_limit) {
            m_merchant_position = 0;
        }

        // Update processing time statistics
        DWORD process_time = GetTickCount() - run_tick;
        if (process_time < m_process_merchant_time_min || m_process_merchant_time_min == 0) {
            m_process_merchant_time_min = process_time;
        }
        if (process_time > m_process_merchant_time_max) {
            m_process_merchant_time_max = process_time;
        }

    TRY_END
}

// ProcessNpcs - Process all NPCs (based on original ProcessNpcs)
void UserEngine::ProcessNpcs() {
    TRY_BEGIN
        DWORD run_tick = GetTickCount();

        int i = m_npc_position;
        for (auto it = m_quest_npc_list.begin(); it != m_quest_npc_list.end(); ) {
            if (i-- > 0) {
                ++it;
                continue;
            }

            auto npc = *it;
            if (!npc) {
                it = m_quest_npc_list.erase(it);
                continue;
            }

            if (!npc->IsGhost()) {
                DWORD current_tick = GetTickCount();
                if ((current_tick - npc->GetRunTick()) > npc->GetRunTime()) {
                    if ((current_tick - npc->GetSearchTick()) > npc->GetSearchTime()) {
                        npc->SetSearchTick(current_tick);
                        npc->SearchObjectViewRange();
                    }

                    if ((current_tick - npc->GetRunTick()) > npc->GetRunTime()) {
                        npc->SetRunTick(current_tick);
                        npc->Run();
                    }
                }
            } else {
                // Remove ghost NPCs after 60 seconds
                if ((GetTickCount() - npc->GetGhostTick()) > 60000) {
                    it = m_quest_npc_list.erase(it);
                    continue;
                }
            }

            // Check time limit
            if ((GetTickCount() - run_tick) > 50) {  // 50ms limit
                m_npc_position = std::distance(m_quest_npc_list.begin(), it);
                break;
            }

            ++it;
        }

        if (m_npc_position >= static_cast<int>(m_quest_npc_list.size())) {
            m_npc_position = 0;
        }

        // Update processing time statistics
        DWORD process_time = GetTickCount() - run_tick;
        if (process_time < m_process_npc_time_min || m_process_npc_time_min == 0) {
            m_process_npc_time_min = process_time;
        }
        if (process_time > m_process_npc_time_max) {
            m_process_npc_time_max = process_time;
        }

    TRY_END
}

// ProcessMissions - Process missions (placeholder)
void UserEngine::ProcessMissions() {
    TRY_BEGIN
        // Mission processing - placeholder for actual implementation
        // This would handle quest and mission updates
    TRY_END
}

// Process4AECFC - Process special events (placeholder)
void UserEngine::Process4AECFC() {
    TRY_BEGIN
        // Special event processing - placeholder for actual implementation
    TRY_END
}

// ProcessEvents - Process game events (placeholder)
void UserEngine::ProcessEvents() {
    TRY_BEGIN
        // Event processing - placeholder for actual implementation
        // This would handle magic events, timed events, etc.
    TRY_END
}

// ProcessMapDoor - Process map doors (placeholder)
void UserEngine::ProcessMapDoor() {
    TRY_BEGIN
        // Map door processing - placeholder for actual implementation
        // This would handle door opening/closing logic
    TRY_END
}

// MerchantInitialize - Initialize all merchants (based on original MerchantInitialize)
void UserEngine::MerchantInitialize() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing Merchants...");

        int initialized_count = 0;
        for (auto it = m_merchant_list.begin(); it != m_merchant_list.end(); ) {
            auto merchant = *it;
            if (!merchant) {
                it = m_merchant_list.erase(it);
                continue;
            }

            // Find map for merchant
            auto envir = g_MapManager.FindMap(merchant->GetMapName());
            if (envir) {
                merchant->SetPEnvir(envir);
                merchant->Initialize();

                if (merchant->IsAddtoMapSuccess() && !merchant->IsHide()) {
                    g_functions::MainOutMessage("Merchant Initialize fail..." +
                        merchant->GetCharName() + " " + merchant->GetMapName() +
                        "(" + std::to_string(merchant->GetCurrX()) + ":" +
                        std::to_string(merchant->GetCurrY()) + ")");
                    it = m_merchant_list.erase(it);
                    continue;
                } else {
                    merchant->LoadNpcScript();
                    merchant->LoadNPCData();
                    initialized_count++;
                }
            } else {
                g_functions::MainOutMessage(merchant->GetCharName() +
                    " Merchant Initialize fail... (m.PEnvir=nil)");
                it = m_merchant_list.erase(it);
                continue;
            }

            ++it;
        }

        g_functions::MainOutMessage("Initialized " + std::to_string(initialized_count) + " merchants");

    TRY_END
}

// NPCInitialize - Initialize all NPCs (based on original NPCinitialize)
void UserEngine::NPCInitialize() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing NPCs...");

        int initialized_count = 0;
        for (auto it = m_quest_npc_list.begin(); it != m_quest_npc_list.end(); ) {
            auto npc = *it;
            if (!npc) {
                it = m_quest_npc_list.erase(it);
                continue;
            }

            // Find map for NPC
            auto envir = g_MapManager.FindMap(npc->GetMapName());
            if (envir) {
                npc->SetPEnvir(envir);
                npc->Initialize();

                if (npc->IsAddtoMapSuccess() && !npc->IsHide()) {
                    g_functions::MainOutMessage(npc->GetCharName() + " Npc Initialize fail...");
                    it = m_quest_npc_list.erase(it);
                    continue;
                } else {
                    npc->LoadNpcScript();
                    initialized_count++;
                }
            } else {
                g_functions::MainOutMessage(npc->GetCharName() +
                    " Npc Initialize fail... (npc.PEnvir=nil)");
                it = m_quest_npc_list.erase(it);
                continue;
            }

            ++it;
        }

        g_functions::MainOutMessage("Initialized " + std::to_string(initialized_count) + " NPCs");

    TRY_END
}

// GetMonRace - Get monster race by name (based on original GetMonRace)
int UserEngine::GetMonRace(const std::string& mon_name) {
    TRY_BEGIN
        for (auto& monster_info : m_monster_list) {
            if (monster_info && monster_info->name == mon_name) {
                return monster_info->race;
            }
        }
    TRY_END

    return -1;
}

// AddMapMonGenCount - Add map monster generation count (based on original AddMapMonGenCount)
int UserEngine::AddMapMonGenCount(const std::string& map_name, int mon_gen_count) {
    TRY_BEGIN
        // Find existing entry
        for (auto& map_count : m_map_mon_gen_count_list) {
            if (map_count && map_count->map_name == map_name) {
                map_count->mon_gen_count += mon_gen_count;
                return map_count->mon_gen_count;
            }
        }

        // Create new entry
        auto new_count = std::make_shared<MapMonGenCount>();
        new_count->map_name = map_name;
        new_count->mon_gen_count = mon_gen_count;
        new_count->not_hum_time_tick = GetTickCount();
        new_count->make_mon_gen_time_tick = GetTickCount();
        new_count->clear_count = 0;
        new_count->not_hum = true;
        m_map_mon_gen_count_list.push_back(new_count);

        return new_count->mon_gen_count;

    TRY_END

    return -1;
}

// GetMapMonGenCount - Get map monster generation count (based on original GetMapMonGenCount)
std::shared_ptr<MapMonGenCount> UserEngine::GetMapMonGenCount(const std::string& map_name) {
    TRY_BEGIN
        for (auto& map_count : m_map_mon_gen_count_list) {
            if (map_count && map_count->map_name == map_name) {
                return map_count;
            }
        }
    TRY_END

    return nullptr;
}

// Player management methods

// GetPlayObject - Find player by name (based on original GetPlayObject)
std::shared_ptr<PlayObject> UserEngine::GetPlayObject(const std::string& name) {
    TRY_BEGIN
        auto it = m_play_object_list.find(name);
        if (it != m_play_object_list.end()) {
            return it->second;
        }
    TRY_END

    return nullptr;
}

// GetPlayObjectEx - Find player by account and name (based on original GetPlayObjectEx)
std::shared_ptr<PlayObject> UserEngine::GetPlayObjectEx(const std::string& account, const std::string& name) {
    TRY_BEGIN
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetUserID() == account &&
                play_object->GetCharName() == name) {
                return play_object;
            }
        }
    TRY_END

    return nullptr;
}

// GetPlayObjectExOfAutoGetExp - Find player for auto exp (based on original GetPlayObjectExOfAutoGetExp)
std::shared_ptr<PlayObject> UserEngine::GetPlayObjectExOfAutoGetExp(const std::string& account) {
    TRY_BEGIN
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetUserID() == account &&
                play_object->IsNotOnlineAddExp()) {
                return play_object;
            }
        }
    TRY_END

    return nullptr;
}

// InPlayObjectList - Check if player is in list (based on original InPlayObjectList)
bool UserEngine::InPlayObjectList(std::shared_ptr<PlayObject> play_object) {
    TRY_BEGIN
        if (!play_object) return false;

        for (auto& pair : m_play_object_list) {
            if (pair.second == play_object) {
                return true;
            }
        }
    TRY_END

    return false;
}

// KickPlayObjectEx - Kick player by account and name (based on original KickPlayObjectEx)
void UserEngine::KickPlayObjectEx(const std::string& account, const std::string& name) {
    TRY_BEGIN
        auto play_object = GetPlayObjectEx(account, name);
        if (play_object) {
            play_object->MakeGhost();
            g_functions::MainOutMessage("Kicked player: " + name + " (Account: " + account + ")");
        }
    TRY_END
}

// GetOnlineHumCount - Get online human count (based on original GetOnlineHumCount)
int UserEngine::GetOnlineHumCount() {
    return static_cast<int>(m_play_object_list.size());
}

// GetUserCount - Get total user count (based on original GetUserCount)
int UserEngine::GetUserCount() {
    return static_cast<int>(m_play_object_list.size() + m_string_list_0c.size());
}

// GetLoadPlayCount - Get load play count (based on original GetLoadPlayCount)
int UserEngine::GetLoadPlayCount() {
    std::lock_guard<std::mutex> lock(m_load_play_section);
    return static_cast<int>(m_load_play_list.size());
}

// GetAutoAddExpPlayCount - Get auto add exp play count (based on original GetAutoAddExpPlayCount)
int UserEngine::GetAutoAddExpPlayCount() {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && play_object->IsNotOnlineAddExp()) {
                count++;
            }
        }
        return count;
    TRY_END

    return 0;
}

// GetAutoAddExpPlayObjectCount - Property implementation
int UserEngine::GetAutoAddExpPlayObjectCount() const {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && play_object->IsNotOnlineAddExp()) {
                count++;
            }
        }
        return count;
    TRY_END

    return 0;
}

// AddUserOpenInfo - Add user open info (based on original AddUserOpenInfo)
void UserEngine::AddUserOpenInfo(std::shared_ptr<UserOpenInfo> user_open_info) {
    TRY_BEGIN
        if (!user_open_info) return;

        std::lock_guard<std::mutex> lock(m_load_play_section);
        m_load_play_list.push_back(user_open_info);

    TRY_END
}

// KickOnlineUser - Kick online user (based on original KickOnlineUser)
void UserEngine::KickOnlineUser(const std::string& chr_name) {
    TRY_BEGIN
        auto play_object = GetPlayObject(chr_name);
        if (play_object) {
            play_object->MakeGhost();
            g_functions::MainOutMessage("Kicked online user: " + chr_name);
        }
    TRY_END
}

// SaveHumanRcd - Save human record (based on original SaveHumanRcd)
void UserEngine::SaveHumanRcd(std::shared_ptr<PlayObject> play_object) {
    TRY_BEGIN
        if (!play_object) return;

        // Save player data - placeholder for actual implementation
        // This would save player data to database
        // FrontEngine.SaveHumanRcd(play_object);

    TRY_END
}

// AddToHumanFreeList - Add to human free list (based on original AddToHumanFreeList)
void UserEngine::AddToHumanFreeList(std::shared_ptr<PlayObject> play_object) {
    TRY_BEGIN
        if (!play_object) return;

        play_object->SetGhostTick(GetTickCount());
        m_play_object_free_list.push_back(play_object);

    TRY_END
}

// Helper methods for player creation

// GetHumData - Get human data (based on original GetHumData)
void UserEngine::GetHumData(std::shared_ptr<PlayObject> play_object, HumDataInfo& human_rcd) {
    TRY_BEGIN
        if (!play_object) return;

        // Copy data from HumDataInfo to PlayObject
        // This is a placeholder - actual implementation would copy all fields
        play_object->SetCharName(human_rcd.char_name);
        play_object->SetLevel(human_rcd.level);
        play_object->SetJob(human_rcd.job);
        play_object->SetGender(human_rcd.gender);
        play_object->SetHP(human_rcd.hp);
        play_object->SetMaxHP(human_rcd.max_hp);
        play_object->SetMP(human_rcd.mp);
        play_object->SetMaxMP(human_rcd.max_mp);
        play_object->SetExp(human_rcd.exp);
        play_object->SetMaxExp(human_rcd.max_exp);
        play_object->SetGold(human_rcd.gold);

        // Copy position data
        play_object->SetMapName(human_rcd.map_name);
        play_object->SetCurrX(human_rcd.curr_x);
        play_object->SetCurrY(human_rcd.curr_y);
        play_object->SetDir(human_rcd.dir);

        // Copy home position
        play_object->SetHomeMap(human_rcd.home_map);
        play_object->SetHomeX(human_rcd.home_x);
        play_object->SetHomeY(human_rcd.home_y);

    TRY_END
}

// GetHomeInfo - Get home information (based on original GetHomeInfo)
std::string UserEngine::GetHomeInfo(int& x, int& y) {
    TRY_BEGIN
        // Return default home map and coordinates
        x = g_Config.nHomeX;
        y = g_Config.nHomeY;
        return g_Config.sHomeMap;
    TRY_END

    return "0";  // Default map
}

// GetRandHomeX - Get random home X coordinate (based on original GetRandHomeX)
int UserEngine::GetRandHomeX(std::shared_ptr<PlayObject> play_object) {
    TRY_BEGIN
        if (play_object) {
            return play_object->GetHomeX() - 2 + (rand() % 5);
        }
        return g_Config.nHomeX;
    TRY_END

    return 100;  // Default X
}

// GetRandHomeY - Get random home Y coordinate (based on original GetRandHomeY)
int UserEngine::GetRandHomeY(std::shared_ptr<PlayObject> play_object) {
    TRY_BEGIN
        if (play_object) {
            return play_object->GetHomeY() - 2 + (rand() % 5);
        }
        return g_Config.nHomeY;
    TRY_END

    return 100;  // Default Y
}

// SendSwitchData - Send switch data (based on original SendSwitchData)
bool UserEngine::SendSwitchData(std::shared_ptr<PlayObject> play_object, int server_index) {
    TRY_BEGIN
        if (!play_object) return false;

        // Send switch data to target server - placeholder for actual implementation
        // This would send player data to another server for server switching
        return true;

    TRY_END

    return false;
}

// SendChangeServer - Send change server (based on original SendChangeServer)
void UserEngine::SendChangeServer(std::shared_ptr<PlayObject> play_object, int server_index) {
    TRY_BEGIN
        if (!play_object) return;

        // Send change server message - placeholder for actual implementation
        // This would notify the client to connect to a different server

    TRY_END
}

// Broadcasting and messaging methods

// SendBroadCastMsg - Send broadcast message (based on original SendBroadCastMsg)
void UserEngine::SendBroadCastMsg(const std::string& msg, TMsgType msg_type) {
    TRY_BEGIN
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && !play_object->IsGhost()) {
                play_object->SysMsg(msg, c_Red, msg_type);
            }
        }
    TRY_END
}

// SendBroadCastMsgExt - Send broadcast message extended (based on original SendBroadCastMsgExt)
void UserEngine::SendBroadCastMsgExt(const std::string& msg, TMsgType msg_type) {
    TRY_BEGIN
        // Extended broadcast with additional filtering
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && !play_object->IsGhost() && play_object->IsReadyRun()) {
                play_object->SysMsg(msg, c_Red, msg_type);
            }
        }
    TRY_END
}

// SendServerGroupMsg - Send server group message (based on original SendServerGroupMsg)
void UserEngine::SendServerGroupMsg(int code, int server_idx, const std::string& msg) {
    TRY_BEGIN
        // Send message to server group - placeholder for actual implementation
        // This would send inter-server messages
    TRY_END
}

// CryCry - Send area message (based on original CryCry)
void UserEngine::CryCry(WORD ident, std::shared_ptr<Environment> map, int x, int y, int wide,
                       BYTE ft_color, BYTE b_color, const std::string& msg) {
    TRY_BEGIN
        if (!map) return;

        // Send message to all players in area
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetPEnvir() == map &&
                abs(play_object->GetCurrX() - x) <= wide &&
                abs(play_object->GetCurrY() - y) <= wide) {

                play_object->SysMsg(msg, static_cast<TMsgColor>(ft_color), t_Notice);
            }
        }
    TRY_END
}

// OnServerStateChanged - Handle server state changes
void UserEngine::OnServerStateChanged(ServerState new_state) {
    TRY_BEGIN
        switch (new_state) {
            case ServerState::STOPPING:
                // Prepare for shutdown
                SaveAllUsers();
                g_functions::MainOutMessage("UserEngine: Server stopping, saved all users");
                break;
            case ServerState::SERVER_ERROR:
                // Handle error state
                g_functions::MainOutMessage("UserEngine: Server error state detected");
                break;
            default:
                break;
        }
    TRY_END
}

// Additional helper methods and stubs for remaining functionality

// ProcessLineNotices - Helper method declaration (already implemented above)
void UserEngine::ProcessLineNotices(std::shared_ptr<PlayObject> play_object, DWORD current_tick);

// MakeNewHuman - Helper method declaration (already implemented above)
std::shared_ptr<PlayObject> UserEngine::MakeNewHuman(std::shared_ptr<UserOpenInfo> user_open_info);

// Stub implementations for remaining methods that need basic functionality

// Item management stubs
std::shared_ptr<StdItem> UserEngine::GetStdItem(int item_idx) {
    TRY_BEGIN
        for (auto& item : m_std_item_list) {
            if (item && item->idx == item_idx) {
                return item;
            }
        }
    TRY_END
    return nullptr;
}

std::shared_ptr<StdItem> UserEngine::GetStdItem(const std::string& item_name) {
    TRY_BEGIN
        for (auto& item : m_std_item_list) {
            if (item && item->name == item_name) {
                return item;
            }
        }
    TRY_END
    return nullptr;
}

int UserEngine::GetStdItemWeight(int item_idx) {
    auto item = GetStdItem(item_idx);
    return item ? item->weight : 0;
}

std::string UserEngine::GetStdItemName(int item_idx) {
    auto item = GetStdItem(item_idx);
    return item ? item->name : "";
}

int UserEngine::GetStdItemIdx(const std::string& item_name) {
    auto item = GetStdItem(item_name);
    return item ? item->idx : -1;
}

// Magic management stubs
std::shared_ptr<Magic> UserEngine::FindMagic(const std::string& magic_name) {
    TRY_BEGIN
        for (auto& magic : m_magic_list) {
            if (magic && magic->name == magic_name) {
                return magic;
            }
        }
    TRY_END
    return nullptr;
}

std::shared_ptr<Magic> UserEngine::FindMagic(int mag_idx) {
    TRY_BEGIN
        for (auto& magic : m_magic_list) {
            if (magic && magic->idx == mag_idx) {
                return magic;
            }
        }
    TRY_END
    return nullptr;
}

bool UserEngine::AddMagic(std::shared_ptr<Magic> magic) {
    TRY_BEGIN
        if (magic) {
            m_magic_list.push_back(magic);
            return true;
        }
    TRY_END
    return false;
}

bool UserEngine::DelMagic(WORD magic_id) {
    TRY_BEGIN
        for (auto it = m_magic_list.begin(); it != m_magic_list.end(); ++it) {
            if (*it && (*it)->idx == magic_id) {
                m_magic_list.erase(it);
                return true;
            }
        }
    TRY_END
    return false;
}

// Merchant and NPC management stubs
std::shared_ptr<Merchant> UserEngine::FindMerchant(std::shared_ptr<BaseObject> merchant) {
    TRY_BEGIN
        for (auto& merch : m_merchant_list) {
            if (merch.get() == merchant.get()) {
                return merch;
            }
        }
    TRY_END
    return nullptr;
}

std::shared_ptr<NormNpc> UserEngine::FindNPC(std::shared_ptr<BaseObject> guild_official) {
    TRY_BEGIN
        for (auto& npc : m_quest_npc_list) {
            if (npc.get() == guild_official.get()) {
                return npc;
            }
        }
    TRY_END
    return nullptr;
}

bool UserEngine::InMerchantList(std::shared_ptr<Merchant> merchant) {
    TRY_BEGIN
        for (auto& merch : m_merchant_list) {
            if (merch == merchant) {
                return true;
            }
        }
    TRY_END
    return false;
}

bool UserEngine::InQuestNPCList(std::shared_ptr<NormNpc> npc) {
    TRY_BEGIN
        for (auto& quest_npc : m_quest_npc_list) {
            if (quest_npc == npc) {
                return true;
            }
        }
    TRY_END
    return false;
}

void UserEngine::AddMerchant(std::shared_ptr<Merchant> merchant) {
    TRY_BEGIN
        if (merchant) {
            m_merchant_list.push_back(merchant);
        }
    TRY_END
}

// Monster management stubs
std::shared_ptr<BaseObject> UserEngine::RegenMonsterByName(const std::string& map, int x, int y, const std::string& mon_name) {
    TRY_BEGIN
        // Placeholder for monster regeneration
        return AddBaseObject(map, x, y, GetMonRace(mon_name), mon_name);
    TRY_END
    return nullptr;
}

std::shared_ptr<BaseObject> UserEngine::RegenPlayByName(std::shared_ptr<PlayObject> play_object, int x, int y, const std::string& mon_name) {
    TRY_BEGIN
        // Placeholder for play object regeneration
        return AddPlayObject(play_object, x, y, mon_name);
    TRY_END
    return nullptr;
}

bool UserEngine::InsMonstersList(std::shared_ptr<MonGenInfo> mon_gen, std::shared_ptr<AnimalObject> monster) {
    TRY_BEGIN
        if (mon_gen && monster) {
            for (auto& existing_mon_gen : m_mon_gen_list) {
                if (existing_mon_gen == mon_gen) {
                    for (auto& obj : existing_mon_gen->cert_list) {
                        if (obj.get() == monster.get()) {
                            return true;
                        }
                    }
                }
            }
        }
    TRY_END
    return false;
}

bool UserEngine::ClearMonsters(const std::string& map_name) {
    TRY_BEGIN
        // Clear monsters from specified map
        bool cleared = false;
        for (auto& mon_gen : m_mon_gen_list) {
            if (mon_gen && mon_gen->map_name == map_name) {
                for (auto& monster : mon_gen->cert_list) {
                    if (monster) {
                        monster->SetNoItem(true);
                        monster->SetHP(0);
                        cleared = true;
                    }
                }
            }
        }
        return cleared;
    TRY_END
    return false;
}

// Additional stub implementations for remaining methods

// Map and range methods
int UserEngine::GetMapOfRangeHumanCount(std::shared_ptr<Environment> envir, int x, int y, int range) {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetPEnvir() == envir &&
                abs(play_object->GetCurrX() - x) <= range &&
                abs(play_object->GetCurrY() - y) <= range) {
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

int UserEngine::GetMapHuman(const std::string& map_name) {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && play_object->GetMapName() == map_name) {
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

int UserEngine::GetMapRageHuman(std::shared_ptr<Environment> envir, int rage_x, int rage_y, int rage, std::list<std::shared_ptr<PlayObject>>& list) {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetPEnvir() == envir &&
                abs(play_object->GetCurrX() - rage_x) <= rage &&
                abs(play_object->GetCurrY() - rage_y) <= rage) {
                list.push_back(play_object);
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

int UserEngine::GetMapMonster(std::shared_ptr<Environment> envir, std::list<std::shared_ptr<BaseObject>>& list) {
    TRY_BEGIN
        int count = 0;
        for (auto& mon_gen : m_mon_gen_list) {
            if (mon_gen && mon_gen->envir == envir) {
                for (auto& monster : mon_gen->cert_list) {
                    if (monster) {
                        list.push_back(monster);
                        count++;
                    }
                }
            }
        }
        return count;
    TRY_END
    return 0;
}

int UserEngine::GetMapRangeMonster(std::shared_ptr<Environment> envir, int x, int y, int range, std::list<std::shared_ptr<BaseObject>>& list) {
    TRY_BEGIN
        int count = 0;
        for (auto& mon_gen : m_mon_gen_list) {
            if (mon_gen && mon_gen->envir == envir) {
                for (auto& monster : mon_gen->cert_list) {
                    if (monster &&
                        abs(monster->GetCurrX() - x) <= range &&
                        abs(monster->GetCurrY() - y) <= range) {
                        list.push_back(monster);
                        count++;
                    }
                }
            }
        }
        return count;
    TRY_END
    return 0;
}

int UserEngine::GetMerchantList(std::shared_ptr<Environment> envir, int x, int y, int range, std::list<std::shared_ptr<Merchant>>& tmp_list) {
    TRY_BEGIN
        int count = 0;
        for (auto& merchant : m_merchant_list) {
            if (merchant &&
                merchant->GetPEnvir() == envir &&
                abs(merchant->GetCurrX() - x) <= range &&
                abs(merchant->GetCurrY() - y) <= range) {
                tmp_list.push_back(merchant);
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

int UserEngine::GetNpcList(std::shared_ptr<Environment> envir, int x, int y, int range, std::list<std::shared_ptr<NormNpc>>& tmp_list) {
    TRY_BEGIN
        int count = 0;
        for (auto& npc : m_quest_npc_list) {
            if (npc &&
                npc->GetPEnvir() == envir &&
                abs(npc->GetCurrX() - x) <= range &&
                abs(npc->GetCurrY() - y) <= range) {
                tmp_list.push_back(npc);
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

// Item and utility methods
bool UserEngine::CopyToUserItemFromName(const std::string& item_name, UserItem* item) {
    TRY_BEGIN
        if (!item) return false;

        auto std_item = GetStdItem(item_name);
        if (std_item) {
            item->wIndex = std_item->idx;
            item->MakeIndex = GetTickCount();  // Simple make index
            item->Dura = std_item->dura_max;
            item->DuraMax = std_item->dura_max;
            return true;
        }
    TRY_END
    return false;
}

void UserEngine::RandomUpgradeItem(UserItem* item) {
    TRY_BEGIN
        if (!item) return;

        // Simple random upgrade logic
        if (rand() % 100 < 10) {  // 10% chance
            if (item->btValue[0] < 7) {
                item->btValue[0]++;
            }
        }
    TRY_END
}

void UserEngine::GetUnknowItemValue(UserItem* item) {
    TRY_BEGIN
        if (!item) return;

        // Set unknown item values - placeholder implementation
        for (int i = 0; i < 14; i++) {
            item->btValue[i] = 0;
        }
    TRY_END
}

bool UserEngine::GetHumPermission(const std::string& user_name, std::string& ip_addr, BYTE& permission) {
    TRY_BEGIN
        // Placeholder for permission checking
        ip_addr = "127.0.0.1";
        permission = 0;  // Normal user
        return true;
    TRY_END
    return false;
}

// Door management stubs
bool UserEngine::OpenDoor(std::shared_ptr<Environment> envir, int x, int y) {
    TRY_BEGIN
        // Placeholder for door opening
        return true;
    TRY_END
    return false;
}

bool UserEngine::CloseDoor(std::shared_ptr<Environment> envir, std::shared_ptr<BaseObject> door) {
    TRY_BEGIN
        // Placeholder for door closing
        return true;
    TRY_END
    return false;
}

void UserEngine::SendDoorStatus(std::shared_ptr<Environment> envir, int x, int y, WORD ident, WORD w_x, int door_x, int door_y, int a, const std::string& str) {
    TRY_BEGIN
        // Placeholder for door status sending
    TRY_END
}

// Additional utility methods
bool UserEngine::FindOtherServerUser(const std::string& name, int& server_index) {
    TRY_BEGIN
        // Placeholder for finding users on other servers
        server_index = -1;
        return false;
    TRY_END
    return false;
}

void UserEngine::ProcessUserMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Placeholder for user message processing
        if (play_object && def_msg) {
            // Process message based on type
        }
    TRY_END
}

void UserEngine::ReloadMerchantList() {
    TRY_BEGIN
        // Reload merchant configurations
        MerchantInitialize();
    TRY_END
}

void UserEngine::ReloadNpcList() {
    TRY_BEGIN
        // Reload NPC configurations
        NPCInitialize();
    TRY_END
}

void UserEngine::HumanExpire(const std::string& account) {
    TRY_BEGIN
        // Handle human expiration
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && play_object->GetUserID() == account) {
                play_object->MakeGhost();
                break;
            }
        }
    TRY_END
}

void UserEngine::sub_4AE514(std::shared_ptr<GoldChangeInfo> gold_change_info) {
    TRY_BEGIN
        // Placeholder for gold change processing
        if (gold_change_info) {
            m_change_human_db_gold_list.push_back(gold_change_info);
        }
    TRY_END
}

void UserEngine::ClearMonSayMsg() {
    TRY_BEGIN
        // Clear monster say messages - placeholder
    TRY_END
}

void UserEngine::SendQuestMsg(const std::string& quest_name) {
    TRY_BEGIN
        // Send quest message - placeholder
        SendBroadCastMsg("Quest: " + quest_name, t_Notice);
    TRY_END
}

void UserEngine::ClearMerchantData() {
    TRY_BEGIN
        // Clear merchant data - placeholder
        for (auto& merchant : m_merchant_list) {
            if (merchant) {
                // Clear merchant specific data
            }
        }
    TRY_END
}

// Private method implementations for remaining functionality

// Monster and object creation methods
std::shared_ptr<BaseObject> UserEngine::AddBaseObject(const std::string& map_name, int x, int y, int mon_race, const std::string& mon_name) {
    TRY_BEGIN
        // Create base object - placeholder for actual implementation
        auto base_object = std::make_shared<BaseObject>();
        if (base_object) {
            base_object->SetMapName(map_name);
            base_object->SetCurrX(x);
            base_object->SetCurrY(y);
            base_object->SetRaceServer(mon_race);
            base_object->SetCharName(mon_name);

            // Find environment
            auto envir = g_MapManager.FindMap(map_name);
            if (envir) {
                base_object->SetPEnvir(envir);
                MonInitialize(base_object, mon_name);
            }
        }
        return base_object;
    TRY_END
    return nullptr;
}

std::shared_ptr<BaseObject> UserEngine::AddPlayObject(std::shared_ptr<PlayObject> play_object, int x, int y, const std::string& mon_name) {
    TRY_BEGIN
        if (!play_object) return nullptr;

        // Set position and name
        play_object->SetCurrX(x);
        play_object->SetCurrY(y);
        play_object->SetCharName(mon_name);

        return std::static_pointer_cast<BaseObject>(play_object);
    TRY_END
    return nullptr;
}

void UserEngine::MonInitialize(std::shared_ptr<BaseObject> base_object, const std::string& mon_name) {
    TRY_BEGIN
        if (!base_object) return;

        // Initialize monster - placeholder for actual implementation
        base_object->SetCharName(mon_name);
        base_object->SetHP(100);  // Default HP
        base_object->SetMaxHP(100);

        // Set monster specific properties based on monster info
        for (auto& monster_info : m_monster_list) {
            if (monster_info && monster_info->name == mon_name) {
                base_object->SetRaceServer(monster_info->race);
                base_object->SetLevel(monster_info->level);
                base_object->SetHP(monster_info->hp);
                base_object->SetMaxHP(monster_info->hp);
                break;
            }
        }
    TRY_END
}

// Monster generation and processing methods
bool UserEngine::RegenMonsters(std::shared_ptr<MonGenInfo> mon_gen, int count) {
    TRY_BEGIN
        if (!mon_gen || !mon_gen->envir) return false;

        int generated = 0;
        for (int i = 0; i < count; i++) {
            // Generate random position within area
            int x = mon_gen->x + (rand() % (mon_gen->area_x * 2 + 1)) - mon_gen->area_x;
            int y = mon_gen->y + (rand() % (mon_gen->area_y * 2 + 1)) - mon_gen->area_y;

            // Check if position is valid
            if (mon_gen->envir->CanWalk(x, y, false)) {
                auto monster = AddBaseObject(mon_gen->map_name, x, y, mon_gen->race, mon_gen->mon_name);
                if (monster) {
                    mon_gen->cert_list.push_back(monster);
                    generated++;
                }
            }
        }

        return generated > 0;
    TRY_END
    return false;
}

int UserEngine::GetGenMonCount(std::shared_ptr<MonGenInfo> mon_gen) {
    TRY_BEGIN
        if (!mon_gen) return 0;

        // Count living monsters in this generation
        int count = 0;
        for (auto& monster : mon_gen->cert_list) {
            if (monster && !monster->IsGhost() && monster->GetHP() > 0) {
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

int UserEngine::MonGetRandomItems(std::shared_ptr<BaseObject> mon) {
    TRY_BEGIN
        if (!mon) return 0;

        // Generate random items for monster - placeholder
        int item_count = 0;

        // Simple random item generation
        if (rand() % 100 < 20) {  // 20% chance for item
            item_count = 1 + (rand() % 3);  // 1-3 items
        }

        return item_count;
    TRY_END
    return 0;
}

// Data management methods
void UserEngine::WriteShiftUserData() {
    TRY_BEGIN
        // Write shift user data - placeholder for actual implementation
        // This would save user switching data to files
    TRY_END
}

void UserEngine::GenShiftUserData() {
    TRY_BEGIN
        // Generate shift user data - placeholder for actual implementation
        // This would prepare user data for server switching
    TRY_END
}

std::shared_ptr<BaseObject> UserEngine::GetSwitchData(const std::string& chr_name, int code) {
    TRY_BEGIN
        // Get switch data for character - placeholder
        for (auto& switch_data : m_change_server_list) {
            if (switch_data && switch_data->GetCharName() == chr_name) {
                return switch_data;
            }
        }
    TRY_END
    return nullptr;
}

void UserEngine::LoadSwitchData(std::shared_ptr<BaseObject> switch_data, std::shared_ptr<PlayObject> play_object) {
    TRY_BEGIN
        if (!switch_data || !play_object) return;

        // Load switch data into play object - placeholder
        play_object->SetCurrX(switch_data->GetCurrX());
        play_object->SetCurrY(switch_data->GetCurrY());
        play_object->SetMapName(switch_data->GetMapName());
    TRY_END
}

void UserEngine::DelSwitchData(std::shared_ptr<BaseObject> switch_data) {
    TRY_BEGIN
        if (!switch_data) return;

        // Remove switch data
        for (auto it = m_change_server_list.begin(); it != m_change_server_list.end(); ++it) {
            if (*it == switch_data) {
                m_change_server_list.erase(it);
                break;
            }
        }
    TRY_END
}

bool UserEngine::MapRageHuman(const std::string& map_name, int map_x, int map_y, int rage) {
    TRY_BEGIN
        // Check if there are humans in map rage - placeholder
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetMapName() == map_name &&
                abs(play_object->GetCurrX() - map_x) <= rage &&
                abs(play_object->GetCurrY() - map_y) <= rage) {
                return true;
            }
        }
    TRY_END
    return false;
}

void UserEngine::Stop() {
    TRY_BEGIN
        if (!m_running) {
            g_functions::MainOutMessage("UserEngine is not running");
            return;
        }
        
        g_functions::MainOutMessage("Stopping UserEngine...");
        
        // Save all user data before stopping
        SaveAllUsers();
        
        m_running = false;
        g_functions::MainOutMessage("UserEngine stopped successfully");
        
    TRY_END
}

bool UserEngine::PlayerLogin(std::shared_ptr<PlayObject> player) {
    TRY_BEGIN
        if (!player) return false;
        
        std::lock_guard<std::mutex> lock(m_users_mutex);
        
        // Add player to online users
        // m_online_users[player->GetCharName()] = player;
        m_online_user_count++;
        
        return true;
        
    TRY_END
    
    return false;
}

bool UserEngine::PlayerLogout(const std::string& char_name) {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_users_mutex);
        
        auto it = m_online_users.find(char_name);
        if (it != m_online_users.end()) {
            m_online_users.erase(it);
            m_online_user_count--;
            return true;
        }
        
        return false;
        
    TRY_END
    
    return false;
}

std::shared_ptr<PlayObject> UserEngine::FindPlayer(const std::string& char_name) {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_users_mutex);
        
        auto it = m_online_users.find(char_name);
        if (it != m_online_users.end()) {
            return it->second;
        }
        
        return nullptr;
        
    TRY_END
    
    return nullptr;
}

void UserEngine::ProcessUsers() {
    TRY_BEGIN
        // Process all online users
        std::lock_guard<std::mutex> lock(m_users_mutex);
        
        for (auto& pair : m_online_users) {
            if (pair.second) {
                // Process user - placeholder for actual implementation
                // pair.second->ProcessUser();
            }
        }
        
    TRY_END
}

void UserEngine::CleanupExpiredSessions() {
    TRY_BEGIN
        // Cleanup expired user sessions
        // This is placeholder for actual implementation
        
    TRY_END
}

void UserEngine::SaveAllUsers() {
    TRY_BEGIN
        g_functions::MainOutMessage("Saving all user data...");
        
        std::lock_guard<std::mutex> lock(m_users_mutex);
        
        for (auto& pair : m_online_users) {
            if (pair.second) {
                // Save user data - placeholder for actual implementation
                // pair.second->SaveUserData();
            }
        }
        
        g_functions::MainOutMessage("All user data saved");
        
    TRY_END
}

void UserEngine::OnServerStateChanged(ServerState new_state) {
    TRY_BEGIN
        // Handle server state changes
        switch (new_state) {
            case ServerState::STOPPING:
                // Prepare for shutdown
                SaveAllUsers();
                break;
            case ServerState::SERVER_ERROR:
                // Handle error state
                break;
            default:
                break;
        }
        
    TRY_END
}

// EmergencyStop - Emergency stop for UserEngine
void UserEngine::EmergencyStop() {
    TRY_BEGIN
        g_functions::MainOutMessage("UserEngine emergency stop initiated");

        // Save all user data immediately
        SaveAllUsers();

        // Force stop
        m_running = false;

        // Clear all active connections
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object) {
                play_object->MakeGhost();
                SaveHumanRcd(play_object);
            }
        }

        g_functions::MainOutMessage("UserEngine emergency stop completed");

    TRY_END
}

// Additional private method implementations

// MonGetRandomItems - Generate random items for monster (based on original MonGetRandomItems)
int UserEngine::MonGetRandomItems(std::shared_ptr<BaseObject> mon) {
    TRY_BEGIN
        if (!mon) return 0;

        // Simple random item generation based on monster level and type
        int item_count = 0;
        int monster_level = mon->GetLevel();

        // Base chance for item drop (20% for normal monsters)
        int drop_chance = 20;
        if (monster_level > 30) drop_chance += 10;  // Higher level monsters drop more
        if (monster_level > 50) drop_chance += 10;

        // Check for item drop
        if (rand() % 100 < drop_chance) {
            item_count = 1;

            // Chance for multiple items (rare)
            if (rand() % 100 < 5) {  // 5% chance for 2 items
                item_count = 2;
            }
        }

        // Boss monsters have better drop rates
        if (mon->GetRaceServer() >= 100) {  // Assuming boss monsters have race >= 100
            item_count += 1;  // Bosses always drop at least one item
        }

        return item_count;

    TRY_END
    return 0;
}

// WriteShiftUserData - Write shift user data (based on original WriteShiftUserData)
void UserEngine::WriteShiftUserData() {
    TRY_BEGIN
        // Write user switching data to files
        // This would save data for server switching functionality

        for (auto& play_object : m_play_object_free_list) {
            if (play_object && play_object->IsSwitchData()) {
                // Save switch data to file
                // Placeholder for actual file writing implementation
                std::string filename = "SwitchData\\" + play_object->GetCharName() + ".dat";
                // WriteUserSwitchData(filename, play_object);
            }
        }

    TRY_END
}

// GenShiftUserData - Generate shift user data (based on original GenShiftUserData)
void UserEngine::GenShiftUserData() {
    TRY_BEGIN
        // Generate user data for server switching
        // This prepares user data for transfer to another server

        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && play_object->IsSwitchData()) {
                // Generate switch data structure
                // Placeholder for actual data generation
                play_object->SetSwitchData(true);
            }
        }

    TRY_END
}

// GetGenMonCount - Get generation monster count (based on original GetGenMonCount)
int UserEngine::GetGenMonCount(std::shared_ptr<MonGenInfo> mon_gen) {
    TRY_BEGIN
        if (!mon_gen) return 0;

        // Count living monsters in this generation
        int count = 0;
        for (auto& monster : mon_gen->cert_list) {
            if (monster && !monster->IsGhost() && monster->GetHP() > 0) {
                count++;
            }
        }
        return count;

    TRY_END
    return 0;
}

// RegenMonsters - Regenerate monsters (based on original RegenMonsters)
bool UserEngine::RegenMonsters(std::shared_ptr<MonGenInfo> mon_gen, int count) {
    TRY_BEGIN
        if (!mon_gen || !mon_gen->envir || count <= 0) return false;

        int generated = 0;
        int max_attempts = count * 10;  // Prevent infinite loops

        for (int attempt = 0; attempt < max_attempts && generated < count; attempt++) {
            // Generate random position within area
            int x = mon_gen->x + (rand() % (mon_gen->area_x * 2 + 1)) - mon_gen->area_x;
            int y = mon_gen->y + (rand() % (mon_gen->area_y * 2 + 1)) - mon_gen->area_y;

            // Validate position bounds
            if (x < 0 || y < 0) continue;

            // Check if position is walkable
            if (mon_gen->envir->CanWalk(x, y, false)) {
                auto monster = AddBaseObject(mon_gen->map_name, x, y, mon_gen->race, mon_gen->mon_name);
                if (monster) {
                    mon_gen->cert_list.push_back(monster);
                    generated++;
                    m_monster_count++;
                }
            }
        }

        return generated > 0;

    TRY_END
    return false;
}

// AddBaseObject - Add base object (enhanced implementation)
std::shared_ptr<BaseObject> UserEngine::AddBaseObject(const std::string& map_name, int x, int y, int mon_race, const std::string& mon_name) {
    TRY_BEGIN
        // Create base object based on race type
        std::shared_ptr<BaseObject> base_object;

        if (mon_race == RC_PLAYOBJECT) {
            // Create PlayObject
            base_object = std::make_shared<PlayObject>();
        } else {
            // Create regular BaseObject for monsters/NPCs
            base_object = std::make_shared<BaseObject>();
        }

        if (!base_object) return nullptr;

        // Set basic properties
        base_object->SetMapName(map_name);
        base_object->SetCurrX(x);
        base_object->SetCurrY(y);
        base_object->SetRaceServer(mon_race);
        base_object->SetCharName(mon_name);

        // Find and set environment
        auto envir = g_MapManager.FindMap(map_name);
        if (envir) {
            base_object->SetPEnvir(envir);

            // Initialize based on type
            if (mon_race != RC_PLAYOBJECT) {
                MonInitialize(base_object, mon_name);
            }

            // Add to map if successful
            if (envir->AddObject(base_object, x, y)) {
                return base_object;
            }
        }

        return nullptr;

    TRY_END
    return nullptr;
}

// MonInitialize - Initialize monster (enhanced implementation)
void UserEngine::MonInitialize(std::shared_ptr<BaseObject> base_object, const std::string& mon_name) {
    TRY_BEGIN
        if (!base_object) return;

        // Set basic monster properties
        base_object->SetCharName(mon_name);

        // Find monster info in monster list
        std::shared_ptr<MonsterInfo> monster_info = nullptr;
        for (auto& info : m_monster_list) {
            if (info && info->name == mon_name) {
                monster_info = info;
                break;
            }
        }

        if (monster_info) {
            // Set properties from monster info
            base_object->SetRaceServer(monster_info->race);
            base_object->SetLevel(monster_info->level);
            base_object->SetHP(monster_info->hp);
            base_object->SetMaxHP(monster_info->hp);
            base_object->SetMP(monster_info->mp);
            base_object->SetMaxMP(monster_info->mp);
            base_object->SetExp(monster_info->exp);

            // Set abilities
            base_object->SetAC(monster_info->ac);
            base_object->SetMAC(monster_info->mac);
            base_object->SetDC(monster_info->dc);
            base_object->SetMC(monster_info->mc);
            base_object->SetSC(monster_info->sc);

            // Set movement and combat properties
            base_object->SetWalkSpeed(monster_info->walk_speed);
            base_object->SetAttackSpeed(monster_info->attack_speed);
            base_object->SetHitPoint(monster_info->hit);
        } else {
            // Set default values if monster info not found
            base_object->SetLevel(1);
            base_object->SetHP(100);
            base_object->SetMaxHP(100);
            base_object->SetMP(0);
            base_object->SetMaxMP(0);
            base_object->SetExp(10);
        }

        // Set initial state
        base_object->SetDir(rand() % 8);  // Random direction
        base_object->SetGhost(false);
        base_object->SetDeath(false);

        // Initialize timing
        DWORD current_tick = GetTickCount();
        base_object->SetRunTick(current_tick);
        base_object->SetSearchTick(current_tick);

    TRY_END
}

// Enhanced user and message processing methods - removed duplicate ProcessUserMessage

// Helper methods for string processing - following original project logic
std::string UserEngine::DecodeString(const std::string& encoded_str) {
    TRY_BEGIN
        // Simple decode implementation - in original project this was more complex
        // For now, return the string as-is, can be enhanced later
        return encoded_str;
    TRY_END
    return "";
}

std::string UserEngine::EncodeString(const std::string& plain_str) {
    TRY_BEGIN
        // Simple encode implementation - in original project this was more complex
        // For now, return the string as-is, can be enhanced later
        return plain_str;
    TRY_END
    return "";
}

// Helper methods for message processing (placeholders for actual implementation)
void UserEngine::ProcessQueryUserState(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Send user state information
        DefaultMessage response;
        response.ident = SM_QUERYUSERSTATE;
        response.recog = def_msg->recog;
        response.param = play_object->GetLevel();
        response.tag = play_object->GetJob();
        response.series = play_object->GetGender();

        // Send response - placeholder for actual network sending
        // play_object->SendMessage(&response, nullptr);

    TRY_END
}

void UserEngine::ProcessQueryBagItems(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Send bag items information
        // This would send the player's inventory items
        // Placeholder for actual implementation

    TRY_END
}

void UserEngine::ProcessQueryUserSet(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Send user settings information
        // This would send player configuration settings
        // Placeholder for actual implementation

    TRY_END
}

void UserEngine::ProcessItemUpgrade(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle item upgrade request
        // This would process item enhancement/strengthening
        if (buff) {
            UserItem* item = reinterpret_cast<UserItem*>(buff);
            RandomUpgradeItem(item);
        }

    TRY_END
}

void UserEngine::ProcessDropItem(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle drop item request
        // This would remove item from inventory and place on ground
        // Placeholder for actual implementation

    TRY_END
}

void UserEngine::ProcessPickupItem(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle pickup item request
        // This would add ground item to player inventory
        // Placeholder for actual implementation

    TRY_END
}

void UserEngine::ProcessSayMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle chat message
        if (buff) {
            std::string message(buff);

            // Broadcast message to nearby players
            auto envir = play_object->GetPEnvir();
            if (envir) {
                std::list<std::shared_ptr<PlayObject>> nearby_players;
                GetMapRageHuman(envir, play_object->GetCurrX(), play_object->GetCurrY(), 12, nearby_players);

                for (auto& nearby_player : nearby_players) {
                    if (nearby_player && nearby_player != play_object) {
                        // Send message to nearby player
                        // nearby_player->SendChatMessage(play_object->GetCharName(), message);
                    }
                }
            }
        }

    TRY_END
}

void UserEngine::ProcessWalkMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle movement request
        int new_x = def_msg->param;
        int new_y = def_msg->tag;
        BYTE new_dir = static_cast<BYTE>(def_msg->series);

        // Validate movement
        auto envir = play_object->GetPEnvir();
        if (envir && envir->CanWalk(new_x, new_y, true)) {
            // Update player position
            play_object->SetCurrX(new_x);
            play_object->SetCurrY(new_y);
            play_object->SetDir(new_dir);

            // Notify nearby players of movement
            // ProcessPlayerMovement(play_object);
        }

    TRY_END
}

void UserEngine::ProcessRunMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle running request (similar to walk but faster)
        ProcessWalkMessage(play_object, def_msg, buff);

    TRY_END
}

void UserEngine::ProcessHitMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle attack request
        int target_x = def_msg->param;
        int target_y = def_msg->tag;
        BYTE attack_dir = static_cast<BYTE>(def_msg->series);

        // Find target at position
        auto envir = play_object->GetPEnvir();
        if (envir) {
            auto target = envir->GetObject(target_x, target_y);
            if (target) {
                // Process attack
                // ProcessPlayerAttack(play_object, target, attack_dir);
            }
        }

    TRY_END
}

void UserEngine::ProcessSpellMessage(std::shared_ptr<PlayObject> play_object, DefaultMessage* def_msg, char* buff) {
    TRY_BEGIN
        // Handle magic spell request
        WORD magic_id = static_cast<WORD>(def_msg->param);
        int target_x = def_msg->tag;
        int target_y = def_msg->series;

        // Find magic in player's magic list
        auto magic = FindMagic(magic_id);
        if (magic) {
            // Process magic casting
            // ProcessPlayerMagic(play_object, magic, target_x, target_y);
        }

    TRY_END
}

// Enhanced monster and map management methods

// ProcessMonsters - Enhanced monster processing (based on original ProcessMonsters)
void UserEngine::ProcessMonsters() {
    TRY_BEGIN
        DWORD current_tick = GetTickCount();
        bool process_limit = false;

        // Process monster regeneration every 30 seconds
        if ((current_tick - m_regen_monsters_tick) > 30000) {
            m_regen_monsters_tick = current_tick;
            ProcessMonsterRegeneration();
        }

        // Process existing monsters with time slicing
        int processed_count = 0;
        int start_position = m_monster_process_position;

        for (auto& mon_gen : m_mon_gen_list) {
            if (!mon_gen) continue;

            // Skip to current processing position
            if (processed_count < start_position) {
                processed_count++;
                continue;
            }

            // Process monsters in this generation
            for (auto it = mon_gen->cert_list.begin(); it != mon_gen->cert_list.end(); ) {
                auto monster = *it;
                if (!monster) {
                    it = mon_gen->cert_list.erase(it);
                    continue;
                }

                if (monster->IsGhost() || monster->GetHP() <= 0) {
                    // Remove dead monsters
                    it = mon_gen->cert_list.erase(it);
                    m_monster_count--;
                    continue;
                }

                // Process living monster
                if ((current_tick - monster->GetRunTick()) > monster->GetRunTime()) {
                    monster->SetRunTick(current_tick);

                    // Update monster search range
                    if ((current_tick - monster->GetSearchTick()) > monster->GetSearchTime()) {
                        monster->SetSearchTick(current_tick);
                        monster->SearchObjectViewRange();
                    }

                    // Run monster AI
                    monster->Run();
                }

                ++it;
                processed_count++;

                // Check time limit to prevent blocking
                if ((GetTickCount() - current_tick) > 50) {  // 50ms limit
                    process_limit = true;
                    m_monster_process_position = processed_count;
                    break;
                }
            }

            if (process_limit) break;
        }

        if (!process_limit) {
            m_monster_process_position = 0;
        }

        m_monster_process_count = processed_count;

    TRY_END
}

// ProcessMonsterRegeneration - Handle monster regeneration
void UserEngine::ProcessMonsterRegeneration() {
    TRY_BEGIN
        for (auto& mon_gen : m_mon_gen_list) {
            if (!mon_gen || !mon_gen->envir) continue;

            // Check if regeneration is needed
            int current_count = GetGenMonCount(mon_gen);
            int needed_count = mon_gen->count - current_count;

            if (needed_count > 0) {
                // Check if humans are in the area (affects regeneration)
                bool humans_nearby = MapRageHuman(mon_gen->map_name, mon_gen->x, mon_gen->y, mon_gen->range);

                // Adjust regeneration rate based on conditions
                int regen_count = needed_count;
                if (humans_nearby) {
                    regen_count = std::min(needed_count, 3);  // Slower regen when players nearby
                } else {
                    regen_count = std::min(needed_count, 10); // Faster regen when no players
                }

                // Apply mission generation rate modifier
                if (mon_gen->mission_gen_rate > 0) {
                    regen_count = (regen_count * mon_gen->mission_gen_rate) / 100;
                }

                // Regenerate monsters
                if (regen_count > 0) {
                    RegenMonsters(mon_gen, regen_count);
                }
            }
        }
    TRY_END
}

// Enhanced map and environment management

// MapRageHuman - Check if humans are in map range (enhanced implementation)
bool UserEngine::MapRageHuman(const std::string& map_name, int map_x, int map_y, int rage) {
    TRY_BEGIN
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetMapName() == map_name &&
                !play_object->IsGhost() &&
                abs(play_object->GetCurrX() - map_x) <= rage &&
                abs(play_object->GetCurrY() - map_y) <= rage) {
                return true;
            }
        }
    TRY_END
    return false;
}

// GetMapRageHuman - Get humans in map range (enhanced implementation)
int UserEngine::GetMapRageHuman(std::shared_ptr<Environment> envir, int rage_x, int rage_y, int rage, std::list<std::shared_ptr<PlayObject>>& list) {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object &&
                play_object->GetPEnvir() == envir &&
                !play_object->IsGhost() &&
                abs(play_object->GetCurrX() - rage_x) <= rage &&
                abs(play_object->GetCurrY() - rage_y) <= rage) {
                list.push_back(play_object);
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

// Enhanced NPC and Merchant processing

// ProcessMerchants - Enhanced merchant processing
void UserEngine::ProcessMerchants() {
    TRY_BEGIN
        DWORD run_tick = GetTickCount();
        bool process_limit = false;

        int i = m_merchant_position;
        for (auto it = m_merchant_list.begin(); it != m_merchant_list.end(); ) {
            if (i-- > 0) {
                ++it;
                continue;
            }

            auto merchant = *it;
            if (!merchant) {
                it = m_merchant_list.erase(it);
                continue;
            }

            if (!merchant->IsGhost()) {
                DWORD current_tick = GetTickCount();
                if ((current_tick - merchant->GetRunTick()) > merchant->GetRunTime()) {
                    // Update merchant search range
                    if ((current_tick - merchant->GetSearchTick()) > merchant->GetSearchTime()) {
                        merchant->SetSearchTick(current_tick);
                        merchant->SearchObjectViewRange();
                    }

                    // Run merchant logic
                    if ((current_tick - merchant->GetRunTick()) > merchant->GetRunTime()) {
                        merchant->SetRunTick(current_tick);
                        merchant->Run();

                        // Process merchant-specific logic
                        ProcessMerchantLogic(merchant);
                    }
                }
            } else {
                // Remove ghost merchants after timeout
                if ((GetTickCount() - merchant->GetGhostTick()) > 60000) {  // 60 seconds
                    it = m_merchant_list.erase(it);
                    continue;
                }
            }

            // Check time limit
            if ((GetTickCount() - run_tick) > 50) {  // 50ms limit
                m_merchant_position = std::distance(m_merchant_list.begin(), it);
                process_limit = true;
                break;
            }

            ++it;
        }

        if (!process_limit) {
            m_merchant_position = 0;
        }

        // Update processing time statistics
        DWORD process_time = GetTickCount() - run_tick;
        if (process_time < m_process_merchant_time_min || m_process_merchant_time_min == 0) {
            m_process_merchant_time_min = process_time;
        }
        if (process_time > m_process_merchant_time_max) {
            m_process_merchant_time_max = process_time;
        }

    TRY_END
}

// ProcessMerchantLogic - Process merchant-specific logic
void UserEngine::ProcessMerchantLogic(std::shared_ptr<Merchant> merchant) {
    TRY_BEGIN
        if (!merchant) return;

        // Handle merchant inventory refresh
        if (merchant->NeedRefreshGoods()) {
            merchant->RefreshGoods();
        }

        // Handle merchant special events
        if (merchant->HasSpecialEvent()) {
            merchant->ProcessSpecialEvent();
        }

        // Handle merchant announcements
        if (merchant->ShouldAnnounce()) {
            std::string announcement = merchant->GetAnnouncement();
            if (!announcement.empty()) {
                // Broadcast merchant announcement to nearby players
                auto envir = merchant->GetPEnvir();
                if (envir) {
                    std::list<std::shared_ptr<PlayObject>> nearby_players;
                    GetMapRageHuman(envir, merchant->GetCurrX(), merchant->GetCurrY(), 10, nearby_players);

                    for (auto& player : nearby_players) {
                        if (player) {
                            player->SysMsg(announcement, c_Green, t_Notice);
                        }
                    }
                }
            }
        }

    TRY_END
}

// Enhanced data management and statistics

// GetOnlineHumCount - Get online human count (enhanced implementation)
int UserEngine::GetOnlineHumCount() {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && !play_object->IsGhost() && play_object->IsReadyRun()) {
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

// GetUserCount - Get total user count (enhanced implementation)
int UserEngine::GetUserCount() {
    TRY_BEGIN
        return static_cast<int>(m_play_object_list.size() + m_string_list_0c.size());
    TRY_END
    return 0;
}

// GetLoadPlayCount - Get load play count (enhanced implementation)
int UserEngine::GetLoadPlayCount() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_load_play_section);
        return static_cast<int>(m_load_play_list.size());
    TRY_END
    return 0;
}

// GetAutoAddExpPlayCount - Get auto add exp play count (enhanced implementation)
int UserEngine::GetAutoAddExpPlayCount() {
    TRY_BEGIN
        int count = 0;
        for (auto& pair : m_play_object_list) {
            auto play_object = pair.second;
            if (play_object && play_object->IsNotOnlineAddExp()) {
                count++;
            }
        }
        return count;
    TRY_END
    return 0;
}

// Enhanced statistics and monitoring

// GetServerStatistics - Get comprehensive server statistics
void UserEngine::GetServerStatistics(ServerStatistics& stats) {
    TRY_BEGIN
        stats.online_players = GetOnlineHumCount();
        stats.total_players = GetUserCount();
        stats.loading_players = GetLoadPlayCount();
        stats.auto_exp_players = GetAutoAddExpPlayCount();
        stats.total_monsters = m_monster_count;
        stats.total_merchants = static_cast<int>(m_merchant_list.size());
        stats.total_npcs = static_cast<int>(m_quest_npc_list.size());
        stats.free_list_count = static_cast<int>(m_play_object_free_list.size());
        stats.monster_generations = static_cast<int>(m_mon_gen_list.size());

        // Calculate memory usage estimates
        stats.memory_usage_mb = CalculateMemoryUsage();

        // Performance statistics
        stats.human_process_time = m_process_human_loop_time;
        stats.merchant_process_time_min = m_process_merchant_time_min;
        stats.merchant_process_time_max = m_process_merchant_time_max;
        stats.npc_process_time_min = m_process_npc_time_min;
        stats.npc_process_time_max = m_process_npc_time_max;

    TRY_END
}

// CalculateMemoryUsage - Calculate estimated memory usage
int UserEngine::CalculateMemoryUsage() {
    TRY_BEGIN
        int total_bytes = 0;

        // Estimate player object memory
        total_bytes += m_play_object_list.size() * sizeof(PlayObject);

        // Estimate monster memory
        total_bytes += m_monster_count * sizeof(BaseObject);

        // Estimate merchant memory
        total_bytes += m_merchant_list.size() * sizeof(Merchant);

        // Estimate NPC memory
        total_bytes += m_quest_npc_list.size() * sizeof(NormNpc);

        // Estimate item list memory
        total_bytes += m_std_item_list.size() * sizeof(StdItem);

        // Estimate magic list memory
        total_bytes += m_magic_list.size() * sizeof(Magic);

        // Convert to MB
        return total_bytes / (1024 * 1024);

    TRY_END
    return 0;
}

// Enhanced cleanup and maintenance

// CleanupExpiredSessions - Enhanced session cleanup
void UserEngine::CleanupExpiredSessions() {
    TRY_BEGIN
        DWORD current_tick = GetTickCount();
        int cleaned_count = 0;

        // Clean up expired players in free list
        for (auto it = m_play_object_free_list.begin(); it != m_play_object_free_list.end(); ) {
            auto play_object = *it;
            if (!play_object) {
                it = m_play_object_free_list.erase(it);
                cleaned_count++;
                continue;
            }

            // Remove players that have been in free list for more than 10 minutes
            if ((current_tick - play_object->GetGhostTick()) > 600000) {  // 10 minutes
                // Final save before cleanup
                SaveHumanRcd(play_object);
                it = m_play_object_free_list.erase(it);
                cleaned_count++;
                continue;
            }
            ++it;
        }

        // Clean up expired monster generations
        for (auto& mon_gen : m_mon_gen_list) {
            if (mon_gen) {
                for (auto it = mon_gen->cert_list.begin(); it != mon_gen->cert_list.end(); ) {
                    auto monster = *it;
                    if (!monster || monster->IsGhost() ||
                        (current_tick - monster->GetGhostTick()) > 300000) {  // 5 minutes
                        it = mon_gen->cert_list.erase(it);
                        if (monster) {
                            m_monster_count--;
                        }
                        cleaned_count++;
                        continue;
                    }
                    ++it;
                }
            }
        }

        // Clean up expired gold change requests
        for (auto it = m_change_human_db_gold_list.begin(); it != m_change_human_db_gold_list.end(); ) {
            auto gold_info = *it;
            if (!gold_info || (current_tick - gold_info->request_time) > 3600000) {  // 1 hour
                it = m_change_human_db_gold_list.erase(it);
                cleaned_count++;
                continue;
            }
            ++it;
        }

        if (cleaned_count > 0) {
            g_functions::MainOutMessage("Cleaned up " + std::to_string(cleaned_count) + " expired sessions/objects");
        }

    TRY_END
}

// PerformMaintenance - Perform regular maintenance tasks
void UserEngine::PerformMaintenance() {
    TRY_BEGIN
        DWORD current_tick = GetTickCount();

        // Perform maintenance every 5 minutes
        static DWORD last_maintenance = 0;
        if ((current_tick - last_maintenance) < 300000) return;  // 5 minutes

        last_maintenance = current_tick;

        g_functions::MainOutMessage("Performing UserEngine maintenance...");

        // Clean up expired sessions
        CleanupExpiredSessions();

        // Compact data structures
        CompactDataStructures();

        // Update statistics
        UpdateStatistics();

        // Save critical data
        SaveCriticalData();

        g_functions::MainOutMessage("UserEngine maintenance completed");

    TRY_END
}

// CompactDataStructures - Compact and optimize data structures
void UserEngine::CompactDataStructures() {
    TRY_BEGIN
        // Remove null entries from lists
        m_std_item_list.remove_if([](const std::shared_ptr<StdItem>& item) {
            return !item;
        });

        m_magic_list.remove_if([](const std::shared_ptr<Magic>& magic) {
            return !magic;
        });

        m_monster_list.remove_if([](const std::shared_ptr<MonsterInfo>& monster) {
            return !monster;
        });

        m_merchant_list.remove_if([](const std::shared_ptr<Merchant>& merchant) {
            return !merchant;
        });

        m_quest_npc_list.remove_if([](const std::shared_ptr<NormNpc>& npc) {
            return !npc;
        });

        // Clean up empty monster generations
        m_mon_gen_list.remove_if([](const std::shared_ptr<MonGenInfo>& mon_gen) {
            return !mon_gen || mon_gen->cert_list.empty();
        });

    TRY_END
}

// UpdateStatistics - Update internal statistics
void UserEngine::UpdateStatistics() {
    TRY_BEGIN
        // Update online user count
        m_online_user_count = GetOnlineHumCount();

        // Update total user count
        m_total_user_count = GetUserCount();

        // Update max user count if needed
        if (m_online_user_count > m_max_user_count) {
            m_max_user_count = m_online_user_count;
        }

        // Update monster count
        int actual_monster_count = 0;
        for (auto& mon_gen : m_mon_gen_list) {
            if (mon_gen) {
                actual_monster_count += GetGenMonCount(mon_gen);
            }
        }
        m_monster_count = actual_monster_count;

    TRY_END
}

// SaveCriticalData - Save critical data periodically
void UserEngine::SaveCriticalData() {
    TRY_BEGIN
        // Save all online users
        SaveAllUsers();

        // Save server statistics
        // SaveServerStatistics();

        // Save configuration changes
        // SaveConfiguration();

    TRY_END
}
